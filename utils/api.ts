// API configuration and utilities
export const API_URLS = {
  NODE_BACKEND: process.env.NEXT_PUBLIC_NODE_BACKEND_URL || 'http://localhost:3001',
  PYTHON_BACKEND: process.env.NEXT_PUBLIC_PYTHON_BACKEND_URL || 'http://localhost:8000',
  INSTAGRAM_API: process.env.NEXT_PUBLIC_INSTAGRAM_API_URL || 'http://localhost:3010',
};

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export async function fetchWithTimeout(
  url: string,
  options: RequestInit & { timeout?: number; requestId?: string } = {}
): Promise<Response> {
  const { timeout = 30000, requestId, ...fetchOptions } = options;

  // Use provided signal or create a new one
  const controller = new AbortController();
  const signal = options.signal || controller.signal;

  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // Add request ID to headers for debugging (only for non-GET requests to avoid CORS preflight)
    const headers = new Headers(fetchOptions.headers);
    if (requestId && fetchOptions.method && fetchOptions.method !== 'GET' && fetchOptions.method !== 'HEAD') {
      headers.set('X-Request-ID', requestId);
    }

    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      signal,
    });

    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

export async function apiRequest<T>(
  url: string,
  options: RequestInit & { timeout?: number; requestId?: string } = {}
): Promise<T> {
  const { requestId, ...fetchOptions } = options;

  // Generate request ID if not provided
  const finalRequestId = requestId || `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // Only add Content-Type header for requests with body (POST, PUT, PATCH, etc.)
  const headers: HeadersInit = {};
  if (fetchOptions.body && fetchOptions.method !== 'GET' && fetchOptions.method !== 'HEAD') {
    headers['Content-Type'] = 'application/json';
  }

  const response = await fetchWithTimeout(url, {
    headers: {
      ...headers,
      ...fetchOptions.headers,
    },
    requestId: finalRequestId,
    ...fetchOptions,
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
    } catch {
      // If we can't parse the error response, use the status text
      errorMessage = response.statusText || errorMessage;
    }
    throw new ApiError(errorMessage, response.status);
  }

  return response.json();
}

// Specific API functions
export const twitterApi = {
  processTweet: (tweetId: string, signal?: AbortSignal, requestId?: string) =>
    apiRequest<{ response: string[] }>(`${API_URLS.NODE_BACKEND}/api/process-tweet`, {
      method: 'POST',
      body: JSON.stringify({ tweetId }),
      signal,
      requestId,
    }),
};

export const tiktokApi = {
  generateResponse: (videoUrl: string, signal?: AbortSignal, requestId?: string) =>
    apiRequest<{ response: string[] }>(`${API_URLS.PYTHON_BACKEND}/generate_tiktok_response`, {
      method: 'POST',
      body: JSON.stringify({ video_url: videoUrl }),
      signal,
      requestId,
    }),
};

export const instagramApi = {
  getPost: (shortcode: string, signal?: AbortSignal, requestId?: string) =>
    apiRequest<any>(`${API_URLS.INSTAGRAM_API}/api/instagram/p/${shortcode}`, {
      signal,
      requestId,
    }),

  generatePhotoResponse: (photoData: any, signal?: AbortSignal, requestId?: string) =>
    apiRequest<{ response: string[] }>(`${API_URLS.PYTHON_BACKEND}/generate_instagram_response_photo`, {
      method: 'POST',
      body: JSON.stringify({ photo_data: photoData }),
      signal,
      requestId,
    }),

  downloadVideo: (url: string, filename: string, signal?: AbortSignal, requestId?: string) =>
    apiRequest<{ path: string }>(`${API_URLS.INSTAGRAM_API}/api/download-proxy?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(filename)}`, {
      signal,
      requestId,
    }),

  generateVideoResponse: (path: string, coauthor_producers: any, edge_media_to_caption: any, signal?: AbortSignal, requestId?: string) =>
    apiRequest<{ response: string[] }>(`${API_URLS.PYTHON_BACKEND}/generate_instagram_response_video`, {
      method: 'POST',
      body: JSON.stringify({
        path,
        coauthor_producers,
        edge_media_to_caption
      }),
      signal,
      requestId,
    }),
};
