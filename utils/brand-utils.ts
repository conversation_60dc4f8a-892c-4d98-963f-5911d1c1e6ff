export interface BrandInfo {
  name: string
  slug: string
  fullName: string
}

export const brandMappings: BrandInfo[] = [
  {
    name: "NYX Professional...",
    slug: "nyx",
    fullName: "NYX Professional Makeup"
  },
  {
    name: "<PERSON><PERSON>",
    slug: "belif", 
    fullName: "<PERSON><PERSON>"
  },
  {
    name: "<PERSON> Roche-Posay",
    slug: "la-roche-posay",
    fullName: "La Roche-Posay"
  },
  {
    name: "Maybelline Ne...",
    slug: "maybelline",
    fullName: "Maybelline New York"
  },
  {
    name: "<PERSON>hayers Natural...",
    slug: "thayers",
    fullName: "Thayers Natural Remedies"
  },
  {
    name: "EyeBuyDirect",
    slug: "eyebuydirect",
    fullName: "EyeBuyDirect"
  },
  {
    name: "THINK",
    slug: "think",
    fullName: "THINK"
  },
  {
    name: "Blueair",
    slug: "blueair",
    fullName: "Blueair"
  },
  {
    name: "This is a Very Lo...",
    slug: "very-long-brand",
    fullName: "This is a Very Long Brand Name"
  }
]

export function getBrandBySlug(slug: string): BrandInfo | undefined {
  return brandMappings.find(brand => brand.slug === slug)
}

export function getBrandByName(name: string): BrandInfo | undefined {
  return brandMappings.find(brand => brand.name === name)
}

export function getSlugFromName(name: string): string {
  const brand = getBrandByName(name)
  return brand?.slug || name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '')
}
