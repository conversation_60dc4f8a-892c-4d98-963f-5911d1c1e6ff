"use client"

import { useState } from "react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { getSlugFromName } from "@/utils/brand-utils"
import {
  ChevronRight,
  ChevronDown,
  BarChart3,
  MessageSquare,
} from "lucide-react"

export interface Brand {
  name: string
  active: boolean
  messageCount: number
  analyticsCount?: number | null
  expanded: boolean
}

interface BrandDropdownProps {
  brands: Brand[]
  onBrandUpdate: (brands: Brand[]) => void
}

export function BrandDropdown({
  brands,
  onBrandUpdate
}: BrandDropdownProps) {
  const toggleBrandExpansion = (index: number) => {
    const updatedBrands = brands.map((brand, i) => 
      i === index ? { ...brand, expanded: !brand.expanded } : brand
    )
    onBrandUpdate(updatedBrands)
  }

  return (
    <div className="flex-1 p-4">
      <div className="mb-2 flex items-center justify-between">
        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Brands</span>
        <button className="text-gray-400 hover:text-gray-600">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
      </div>
      <div className="space-y-1">
        {brands.map((brand, index) => (
          <Collapsible key={index} open={brand.expanded} onOpenChange={() => toggleBrandExpansion(index)}>
            <CollapsibleTrigger asChild>
              <div
                className={`flex items-center justify-between p-2 rounded-lg cursor-pointer transition-colors ${
                  brand.active
                    ? "bg-blue-50 text-blue-700 border border-blue-200"
                    : "text-gray-600 hover:bg-gray-50"
                }`}
              >
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-gray-300 rounded"></div>
                  <span className="text-sm">{brand.name}</span>
                </div>
                <div className="flex items-center space-x-1">
                  {brand.messageCount > 0 && (
                    <span className="bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
                      {brand.messageCount}
                    </span>
                  )}
                  {brand.expanded ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="ml-6 mt-1 space-y-1">
              <div
                className="flex items-center space-x-2 p-2 text-gray-600 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
                onClick={() => {
                  const slug = getSlugFromName(brand.name)
                  window.location.href = `/${slug}/analytics`
                }}
              >
                <BarChart3 className="w-4 h-4" />
                <span className="text-sm">Analytics</span>
              </div>
              <div
                className="flex items-center space-x-2 p-2 text-gray-600 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
                onClick={() => {
                  const slug = getSlugFromName(brand.name)
                  window.location.href = `/${slug}/messages`
                }}
              >
                <MessageSquare className="w-4 h-4" />
                <span className="text-sm">Messages</span>
                {brand.messageCount > 0 && (
                  <span className="bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center ml-auto">
                    {brand.messageCount}
                  </span>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>
        ))}
      </div>
    </div>
  )
}
