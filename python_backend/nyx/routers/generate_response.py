import asyncio
import base64
import os
import re
import tempfile
import uuid
from typing import Any, List

import cv2
import httpx
import openai
from asyncio_throttle import Throttler
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# Directory to save media
download_dir = "tweet_media"
os.makedirs(download_dir, exist_ok=True)

# Get API keys from environment variables
OPENAI_API_KEY = os.getenv(
    "OPENAI_API_KEY", "********************************************************"
)
bearer_token = "AAAAAAAAAAAAAAAAAAAAAIvGrQEAAAAAjPcTuuFSWhK%2Fvjd8B5L%2BdYv9WkE%3DyabUj9jDk3HE9tS5bbbwsreuH66TpyGKBqP71XQFlO7r8NdDAK"  # Replace with env var ideally

router = APIRouter()

# Create more permissive throttlers for different operations
media_throttler = Throttler(
    rate_limit=60, period=60
)  # 60 media downloads per 60 seconds (increased from 30)
openai_throttler = Throttler(
    rate_limit=40, period=60
)  # 40 OpenAI calls per 60 seconds (increased from 20)
tiktok_throttler = Throttler(
    rate_limit=1, period=3
)  # 1 TikTok API calls per 3 seconds (kept same as requested)

# Global async OpenAI client pool for better performance
_openai_client_pool = []
_openai_client_lock = asyncio.Lock()
_openai_client_index = 0


async def get_openai_client():
    """Get or create async OpenAI client with improved concurrency."""
    global _openai_client_pool, _openai_client_index

    # Initialize clients if not already done
    async with _openai_client_lock:
        if not _openai_client_pool:
            if not OPENAI_API_KEY:
                raise ValueError("OPENAI_API_KEY not found")

            # Create multiple OpenAI clients for better concurrency
            for _ in range(2):  # Create 2 OpenAI clients
                client = openai.AsyncOpenAI(
                    api_key=OPENAI_API_KEY,
                    max_retries=3,
                    timeout=30.0,
                )
                _openai_client_pool.append(client)

        # Thread-safe round-robin selection for load balancing
        current_index = _openai_client_index
        _openai_client_index = (current_index + 1) % len(_openai_client_pool)
        return _openai_client_pool[current_index]


# Global HTTP client pool for better concurrency
_http_client_pool = []
_http_client_lock = asyncio.Lock()
_http_client_index = 0


async def get_http_client():
    """Get or create async HTTP client with improved concurrency settings."""
    global _http_client_pool, _http_client_index

    async with _http_client_lock:
        if not _http_client_pool:
            # Create multiple HTTP clients for better concurrency
            for _ in range(3):  # Create 3 HTTP clients
                client = httpx.AsyncClient(
                    timeout=httpx.Timeout(30.0),
                    limits=httpx.Limits(
                        max_keepalive_connections=20,  # Increased from 5
                        max_connections=50,  # Increased from 10
                        keepalive_expiry=30.0,  # Keep connections alive longer
                    ),
                    http2=True,  # Enable HTTP/2 for better multiplexing
                )
                _http_client_pool.append(client)

        # Thread-safe round-robin selection for load balancing
        current_index = _http_client_index
        _http_client_index = (current_index + 1) % len(_http_client_pool)
        return _http_client_pool[current_index]


# Define common media extensions (lowercase)
VIDEO_EXTENSIONS = {".mp4", ".mov", ".avi", ".mkv", ".wmv", ".flv", ".webm"}
IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"}


def get_base_messages():
    """Get a fresh copy of base messages for each request to avoid shared state."""
    return [
        {
            "role": "system",
            "content": """
You are Nikki, NYX Professional Makeup's social media voice. NYX is part of L'Oréal's portfolio but maintains its bold, accessible, and unapologetically playful identity.

## Core Rules (READ CAREFULLY)
1. **Length**: 1 sentence maximum. Shorter is better.
2. **Simplicity**: Make ONE observation or give ONE compliment. Don't elaborate.
3. **Emojis**: Use sparingly (0-2 max), only cute/aesthetic ones (✨💕🤩💗🫶). Never 😉😏🔥
4. **No forced references**: Don't reach for pop culture unless it's completely natural
5. **Be genuine, not performative**: Sound impressed, not like you're trying to impress

## Response Formula
Pick ONE:
- Simple compliment + emoji: "this collection is so whimsical 🤩💕"
- Excited reaction: "obsessed with this energy !!!"
- Relatable observation: "the way i NEED this vibe"
- Short hype: "absolutely stunning bestie"

## What GOOD Responses Look Like:
✓ "this collection is so whimsical 🤩💕"
✓ "obsessed with these vibes !!!"
✓ "you're absolutely glowing ✨"
✓ "THIS is the serve we needed"
✓ "so here for this look 💗"
✓ "literally perfect"

## What BAD Responses Look Like:
✗ "omg you're serving Regency realness with this look, bestie!!! i can literally feel the romance in the air... you're def the diamond of the szn. are you ready to find your Duke? 😉✨"
   - Why: Too long, forced reference, trying too hard, flirty emoji

✗ "This is giving main character energy and I'm so here for it queen! 👑✨🔥"
   - Why: Multiple thoughts, cliché phrases, too many emojis

✗ "Wow, this really captures the essence of the collection's aesthetic vision!"
   - Why: Too formal, sounds like marketing copy

## Critical Guidelines:
- If you write more than 10 words, you're probably overthinking it
- If you make a pop culture reference, delete it
- If you use 😉😏🔥💋👑, you're being too extra
- If you explain why something is good, just say it's good instead
- When in doubt: shorter, simpler, sweeter

Remember: You're casually commenting, not writing a caption or trying to go viral. Keep it effortless.
""",
        }
    ]


class TweetDetailsPayload(BaseModel):
    tweetData: Any


class TikTokPayload(BaseModel):
    video_url: Any


class InstagramPayload(BaseModel):
    path: str
    coauthor_producers: Any
    edge_media_to_caption: Any


class InstagramPayloadPhoto(BaseModel):
    photo_data: Any


class AnalysisResponse(BaseModel):
    response: List[str]


def remove_links(text):
    return re.sub(r"https?://\S+", "", text).strip()


async def describe_video_with_gpt(
    base64_frames: List[str], custom_prompt: str = None
) -> str:
    """
    Use GPT-4o to analyze video frames and generate a description.

    Args:
        base64_frames: List of base64-encoded frame strings
        custom_prompt: Optional custom prompt for analysis

    Returns:
        Video description string
    """
    if not base64_frames:
        raise ValueError("No frames provided for analysis")

    try:
        client = await get_openai_client()

        # Default prompt for video description
        default_prompt = """
        Analyze these video frames and provide a detailed description of:
        1. The overall mood or tone
        2. Any text or branding visible

        Provide a comprehensive but concise description in 2-3 paragraphs.
        """

        prompt = custom_prompt if custom_prompt else default_prompt

        # Prepare messages for GPT-4o
        messages = [
            {
                "role": "system",
                "content": "You are an expert video analyst. Analyze the provided video frames and give detailed, accurate descriptions.",
            },
            {
                "role": "user",
                "content": [{"type": "text", "text": prompt}]
                + [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{frame}"},
                    }
                    for frame in base64_frames
                ],
            },
        ]

        # Make API call with throttling
        async with openai_throttler:
            response = await client.chat.completions.create(
                model="gpt-4o-mini",  # Using standard GPT-4o for video analysis
                messages=messages,
                max_tokens=264,
                temperature=0.7,
            )

        if response.choices and response.choices[0].message:
            return response.choices[0].message.content.strip()
        else:
            raise ValueError("No response received from GPT-4o")

    except Exception as e:
        raise ValueError(f"Error analyzing video with GPT: {str(e)}")


@router.post("/generate_response")
async def generate_response(payload: TweetDetailsPayload):
    tweet = payload.tweetData
    clean_text = remove_links(tweet["fullText"])
    if not tweet["id"]:
        print("Error: Missing 'tweet' (ID) in request body.")
        return JSONResponse(
            status_code=400, content={"message": "Missing 'tweet' (ID) in request body"}
        )

    # Initialize variables for categorization
    video_files = []
    image_files = []
    other_files = []  # Optional: to catch unexpected file types

    try:
        # Use per-request message copy and OpenAI client
        tweet_messages = get_base_messages()
        tweet_messages.append({"role": "user", "content": clean_text})

        # Create per-request OpenAI client
        client = await get_openai_client()

        # Get HTTP client from pool for media downloads
        http_client = await get_http_client()

        if tweet.get("media", None):
            for file_path in tweet["media"]:
                try:
                    _, extension = os.path.splitext(file_path["url"])
                    extension_lower = extension.lower()
                    print(extension_lower)
                    print(file_path["url"])

                    if extension_lower in VIDEO_EXTENSIONS:
                        video_files.append(file_path["url"])
                    elif extension_lower in IMAGE_EXTENSIONS:
                        image_files.append(file_path["url"])
                    else:
                        if extension:
                            other_files.append(file_path["url"])
                        print(f"Note: Uncategorized media type: {file_path}")

                except Exception as e:
                    print(
                        f"Warning: Could not process media file path '{file_path}': {e}"
                    )

        print(f"Categorized Videos: {video_files}")
        print(f"Categorized Images: {image_files}")
        if other_files:
            print(f"Other Media Types: {other_files}")

        # Process videos with async HTTP calls and throttling
        for media in video_files:
            async with media_throttler:
                response = await http_client.get(media)
                response.raise_for_status()

                # Save video content to temporary file for cv2 processing
                with tempfile.NamedTemporaryFile(
                    delete=False, suffix=".mp4"
                ) as tmp_file:
                    tmp_file.write(response.content)
                    tmp_path = tmp_file.name

                try:
                    video = cv2.VideoCapture(tmp_path)
                    base64Frames = []
                    frame_count = 0
                    while (
                        video.isOpened() and frame_count < 1000
                    ):  # Limit frames to prevent memory issues
                        success, frame = video.read()
                        if not success:
                            break
                        if frame_count % 100 == 0:  # Sample every 100th frame
                            _, buffer = cv2.imencode(".jpg", frame)
                            base64Frames.append(
                                base64.b64encode(buffer).decode("utf-8")
                            )
                        frame_count += 1
                    video.release()

                    if base64Frames:
                        tweet_messages.append(
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": f"data:image/jpeg;base64,{x}"
                                        },
                                    }
                                    for x in base64Frames[:10]  # Limit to 10 frames max
                                ],
                            }
                        )
                finally:
                    # Clean up temporary file
                    try:
                        os.unlink(tmp_path)
                    except OSError:
                        pass

        # Process images with async HTTP calls and throttling
        for media in image_files:
            async with media_throttler:
                response = await http_client.get(media)
                response.raise_for_status()
                base64_image = base64.b64encode(response.content).decode("utf-8")
                tweet_messages.append(
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                },
                            },
                        ],
                    }
                )

        # Make OpenAI API call with throttling
        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="gpt-4o",
                messages=tweet_messages,
                max_tokens=64,
                n=3,
            )
        print("response_completion done")
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        # Catch errors during processing
        print(f"Error processing tweet {tweet['id']}: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500, content={"message": f"Error processing tweet: {str(e)}"}
        )


ms_token = os.getenv(
    "TIKTOK_MS_TOKEN",
    "tbNLOJaUy7_vBktSLptcACRHmjQNURMWzaWZ-NQPoQsMgYhP6-U0Ca8xIKtKQJ5glmJ_DJr9Epgu_lsJgJY9LAWOhhYftWSR8eVLObKrAK4fyw_76fsXS4OtCuT54ST4iQx_S-ltZYRaAQ==",
)
from TikTokApi import TikTokApi

# TikTok API session pool for better concurrency
_tiktok_api_pool = []
_tiktok_api_lock = asyncio.Lock()
_session_index = 0


async def get_tiktok_api():
    """Get or create TikTok API instance with proper session management."""
    global _tiktok_api_pool, _session_index
    async with _tiktok_api_lock:
        if not _tiktok_api_pool:
            # Create multiple API instances for better concurrency
            for _ in range(1):
                api = TikTokApi()
                await api.create_sessions(
                    ms_tokens=[ms_token],
                    num_sessions=1,  # 2 sessions per API instance
                    sleep_after=3,  # Reduced sleep time
                    browser=os.getenv("TIKTOK_BROWSER", "chromium"),
                )
                _tiktok_api_pool.append(api)

        # Thread-safe round-robin selection for load balancing
        current_index = _session_index
        _session_index = (current_index + 1) % len(_tiktok_api_pool)
        return _tiktok_api_pool[current_index]


@router.post("/generate_tiktok_response")
async def generate_response_tiktok(video_url: TikTokPayload):
    import time

    time.sleep(3)
    try:
        # Get shared TikTok API instance
        api = await get_tiktok_api()

        # Create per-request OpenAI client and message copy
        client = await get_openai_client()
        tiktok_messages = get_base_messages()

        # Get video info with throttling
        async with tiktok_throttler:
            video_info = await api.video(url=video_url.video_url).info()

        tiktok_messages.append({"role": "user", "content": video_info["desc"]})

        # Get session and download video
        _, session = api._get_session()
        downloadAddr = video_info["video"]["downloadAddr"]
        cookies = await api.get_session_cookies(session)

        # Get HTTP client from pool for video download
        http_client = await get_http_client()

        headers = {
            "range": "bytes=0-",
            "accept-encoding": "identity;q=1, *;q=0",
            "referer": "https://www.tiktok.com/",
            **dict(session.headers),
        }

        async with tiktok_throttler:
            resp = await http_client.get(downloadAddr, headers=headers, cookies=cookies)
            resp.raise_for_status()

            # Save video to temporary file with unique name
            unique_id = str(uuid.uuid4())
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=f"_{unique_id}.mp4"
            ) as tmpf:
                tmpf.write(resp.content)
                tmp_path = tmpf.name

            try:
                # Process video frames
                video = cv2.VideoCapture(tmp_path)
                base64Frames = []
                frame_count = 0

                while video.isOpened() and frame_count < 1000:  # Limit frames
                    success, frame = video.read()
                    if not success:
                        break
                    if frame_count % 100 == 0:  # Sample every 100th frame
                        _, buffer = cv2.imencode(".jpg", frame)
                        base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
                    frame_count += 1

                video.release()

                if base64Frames:
                    description = await describe_video_with_gpt(
                        base64_frames=base64Frames,
                    )
                    tiktok_messages.append({"role": "user", "content": description})
            finally:
                # Clean up temporary file
                try:
                    os.unlink(tmp_path)
                except OSError:
                    pass

        # Make OpenAI API call with throttling
        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="gpt-4o",
                messages=tiktok_messages,
                max_tokens=64,
                n=3,
            )
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing TikTok video: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing TikTok video: {str(e)}"},
        )


async def gather_coauthor_info(coauthor_producers, client):
    """Gather information about co-authors using GPT-4o with research capabilities"""
    if not coauthor_producers or len(coauthor_producers) == 0:
        return ""

    coauthor_info_list = []

    for coauthor in coauthor_producers:
        try:
            # Extract username from coauthor object
            username = (
                coauthor.get("username", "") if isinstance(coauthor, dict) else ""
            )
            profille_pic_url = coauthor.get("profile_pic_url", "")

            if username:
                # Use GPT-4o to gather information about the co-author
                search_prompt = f"""
                You are having this profile picture {profille_pic_url} and the username {username}
                These information comes from Instagram. Give me a short description about this person.
                """

                # Make a research request using GPT-4o
                search_response = await client.chat.completions.create(
                    model="gpt-4o-mini-search-preview",
                    web_search_options={},
                    messages=[
                        {
                            "role": "system",
                            "content": "You are a research assistant that provides concise information about social media influencers and content creators.",
                        },
                        {"role": "user", "content": search_prompt},
                    ],
                    max_tokens=150,
                )

                if search_response.choices and search_response.choices[0].message:
                    info = search_response.choices[0].message.content.strip()
                    coauthor_info_list.append(f"@{username}: {info}")

        except Exception as e:
            print(f"Error gathering info for coauthor {username}: {e}")
            continue

    return "\n".join(coauthor_info_list) if coauthor_info_list else ""


@router.post("/generate_instagram_response_video")
async def generate_response_instagram(payload: InstagramPayload):
    try:
        path = payload.path
        coauthor_producers = payload.coauthor_producers
        edge_media_to_caption = payload.edge_media_to_caption

        # Create per-request OpenAI client and message copy
        client = await get_openai_client()
        instagram_messages = get_base_messages()

        # Gather co-author information
        coauthor_info = await gather_coauthor_info(coauthor_producers, client)

        # Extract caption text
        caption_text = ""
        if edge_media_to_caption and isinstance(edge_media_to_caption, dict):
            edges = edge_media_to_caption.get("edges", [])
            if edges and len(edges) > 0:
                node = edges[0].get("node", {})
                caption_text = node.get("text", "")

        # Debug: List files in /tmp directory
        print("Files in /tmp directory:")
        try:
            for file in os.listdir("/tmp"):
                file_path = os.path.join("/tmp", file)
                file_size = (
                    os.path.getsize(file_path)
                    if os.path.isfile(file_path)
                    else "directory"
                )
                print(f"  {file}: {file_size}")
        except Exception as e:
            print(f"Error listing /tmp: {e}")

        if not os.path.exists(path):
            # Try to handle Docker volume path mapping

            # Try alternative paths
            alt_paths = [path, path.replace("/tmp/", "/app/tmp/"), f"/app{path}"]

            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    path = alt_path
                    break
            else:
                return JSONResponse(
                    status_code=400,
                    content={
                        "message": f"File not found at path: {path}. Make sure volumes are properly mounted between services."
                    },
                )

        # Process video with frame limiting
        video = cv2.VideoCapture(path)
        base64Frames = []
        frame_count = 0

        while video.isOpened() and frame_count < 1000:  # Limit frames
            success, frame = video.read()
            if not success:
                break
            if frame_count % 100 == 0:  # Sample every 100th frame
                _, buffer = cv2.imencode(".jpg", frame)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
            frame_count += 1
        video.release()

        # Create enhanced prompt with co-author information and video description
        enhanced_prompt_parts = []

        if caption_text:
            enhanced_prompt_parts.append(f"Instagram post text: {caption_text}")

        if coauthor_info:
            enhanced_prompt_parts.append(
                f"Co-authors/Collaborators Information:\n{coauthor_info}"
            )

        if base64Frames:
            description = await describe_video_with_gpt(
                base64_frames=base64Frames,
            )
            enhanced_prompt_parts.append(f"Video description: {description}")

        enhanced_prompt = "\n\n".join(enhanced_prompt_parts)

        # Make OpenAI API call with throttling using GPT-4.1 (or latest available)
        instagram_messages.append({"role": "user", "content": enhanced_prompt})

        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="gpt-4o",  # Using latest available model instead of GPT-4.1
                messages=instagram_messages,
                max_tokens=256,
                n=3,
                temperature=0.7,  # Slightly higher for more creative responses
            )
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing Instagram video: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing Instagram video: {str(e)}"},
        )


@router.post("/generate_instagram_response_photo")
async def generate_response_instagram_photo(photo_data: InstagramPayloadPhoto):
    try:
        # Create per-request OpenAI client and message copy
        client = await get_openai_client()
        instagram_messages = get_base_messages()

        data = photo_data.photo_data["data"]["xdt_shortcode_media"]
        post_text = data["edge_media_to_caption"]["edges"][0]["node"]["text"]
        instagram_messages.append({"role": "user", "content": post_text})

        image_url = data["display_url"]

        # Get HTTP client from pool for image download
        http_client = await get_http_client()

        async with media_throttler:
            response = await http_client.get(image_url)
            response.raise_for_status()
            base64_image = base64.b64encode(response.content).decode("utf-8")
            instagram_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            },
                        },
                    ],
                }
            )

        # Make OpenAI API call with throttling
        async with openai_throttler:
            response_completion = await client.chat.completions.create(
                model="gpt-4o",
                messages=instagram_messages,
                max_tokens=64,
                n=3,
            )

        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing Instagram photo: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing Instagram photo: {str(e)}"},
        )
