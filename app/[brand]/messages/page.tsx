"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { BrandDropdown, type Brand } from "@/components/brand-dropdown"
import { getBrandBySlug, brandMappings } from "@/utils/brand-utils"
import {
  Search,
  Filter,
  MessageSquare,
  BarChart3,
  Menu,
  ArrowLeft,
  Clock,
  User,
  Heart,
  MessageCircle,
  Share,
  MoreHorizontal
} from "lucide-react"
import Link from "next/link"

// Sample messages data
const sampleMessages = [
  {
    id: 1,
    platform: "Instagram",
    user: "@beautyqueen23",
    message: "Love the new NYX lipstick collection! The colors are amazing 💄✨",
    timestamp: "2 hours ago",
    sentiment: "positive",
    status: "pending",
    engagement: { likes: 45, comments: 12, shares: 3 }
  },
  {
    id: 2,
    platform: "Twitter",
    user: "@makeuplover",
    message: "Has anyone tried the new foundation? Looking for reviews before I buy",
    timestamp: "4 hours ago",
    sentiment: "neutral",
    status: "responded",
    engagement: { likes: 23, comments: 8, shares: 1 }
  },
  {
    id: 3,
    platform: "TikTok",
    user: "@glowup_girl",
    message: "This eyeshadow palette is everything! Tutorial coming soon 👀",
    timestamp: "6 hours ago",
    sentiment: "positive",
    status: "pending",
    engagement: { likes: 156, comments: 34, shares: 12 }
  },
  {
    id: 4,
    platform: "Instagram",
    user: "@skincare_addict",
    message: "The packaging is cute but the product didn't work for my skin type 😕",
    timestamp: "8 hours ago",
    sentiment: "negative",
    status: "pending",
    engagement: { likes: 12, comments: 5, shares: 0 }
  }
]

const initialBrands: Brand[] = brandMappings.map(brand => ({
  name: brand.name,
  active: false,
  messageCount: Math.floor(Math.random() * 20) + 1,
  analyticsCount: null,
  expanded: false
}))

export default function BrandMessagesPage() {
  const params = useParams()
  const brandSlug = params.brand as string
  const brandInfo = getBrandBySlug(brandSlug)
  
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [brandStates, setBrandStates] = useState<Brand[]>(
    initialBrands.map(brand => ({
      ...brand,
      active: brand.name === brandInfo?.name
    }))
  )

  const handleBrandUpdate = (updatedBrands: Brand[]) => {
    setBrandStates(updatedBrands)
  }

  const handleAnalyticsClick = (brandName: string) => {
    const targetBrand = brandMappings.find(b => b.name === brandName)
    if (targetBrand) {
      window.location.href = `/${targetBrand.slug}/analytics`
    }
  }

  const handleMessagesClick = (brandName: string) => {
    const targetBrand = brandMappings.find(b => b.name === brandName)
    if (targetBrand) {
      window.location.href = `/${targetBrand.slug}/messages`
    }
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800'
      case 'negative': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'responded': return 'bg-blue-100 text-blue-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (!brandInfo) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Brand Not Found</h1>
          <p className="text-gray-600 mb-4">The brand "{brandSlug}" could not be found.</p>
          <Link href="/" className="text-blue-600 hover:text-blue-800">
            Return to Home
          </Link>
        </div>
      </div>
    )
  }

  const filteredMessages = sampleMessages.filter(message => {
    const matchesSearch = message.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         message.user.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = filterStatus === "all" || message.status === filterStatus
    return matchesSearch && matchesFilter
  })

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="hidden lg:flex w-64 bg-white border-r border-gray-200 flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
              AC
            </div>
            <span className="font-medium text-gray-900">AC Social Command</span>
          </div>
        </div>

        {/* Brands List */}
        <BrandDropdown
          brands={brandStates}
          onBrandUpdate={handleBrandUpdate}
          onAnalyticsClick={handleAnalyticsClick}
          onMessagesClick={handleMessagesClick}
        />

        {/* Navigation */}
        <div className="p-4 border-t border-gray-200">
          <div className="space-y-1">
            <Link 
              href={`/${brandSlug}/analytics`}
              className="flex items-center space-x-2 p-2 text-gray-600 hover:bg-gray-50 rounded-lg cursor-pointer"
            >
              <BarChart3 className="w-4 h-4" />
              <span className="text-sm">Analytics</span>
            </Link>
            <div className="flex items-center space-x-2 p-2 bg-blue-50 text-blue-700 rounded-lg">
              <MessageSquare className="w-4 h-4" />
              <span className="text-sm font-medium">Messages</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 lg:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="flex items-center space-x-3">
              <Link href="/" className="lg:hidden">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="w-4 h-4" />
                </Button>
              </Link>
              <Button
                variant="outline"
                size="sm"
                className="lg:hidden"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <Menu className="w-4 h-4" />
              </Button>
              <div>
                <h1 className="text-xl lg:text-2xl font-bold text-gray-900">{brandInfo.fullName} - Messages</h1>
                <p className="text-gray-600 mt-1 text-sm lg:text-base">Social media messages and engagement</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                {filteredMessages.filter(m => m.status === 'pending').length} Pending
              </Badge>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4 lg:p-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search messages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={filterStatus === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterStatus("all")}
              >
                All
              </Button>
              <Button
                variant={filterStatus === "pending" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterStatus("pending")}
              >
                Pending
              </Button>
              <Button
                variant={filterStatus === "responded" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterStatus("responded")}
              >
                Responded
              </Button>
            </div>
          </div>

          {/* Messages List */}
          <div className="space-y-4">
            {filteredMessages.map((message) => (
              <Card key={message.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-gray-500" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{message.user}</span>
                          <Badge variant="outline" className="text-xs">
                            {message.platform}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <Clock className="w-3 h-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{message.timestamp}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className={getSentimentColor(message.sentiment)}>
                        {message.sentiment}
                      </Badge>
                      <Badge variant="secondary" className={getStatusColor(message.status)}>
                        {message.status}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-4">{message.message}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Heart className="w-4 h-4" />
                        <span>{message.engagement.likes}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="w-4 h-4" />
                        <span>{message.engagement.comments}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Share className="w-4 h-4" />
                        <span>{message.engagement.shares}</span>
                      </div>
                    </div>
                    
                    {message.status === 'pending' && (
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          Generate Response
                        </Button>
                        <Button size="sm">
                          Respond
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredMessages.length === 0 && (
            <div className="text-center py-12">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
              <p className="text-gray-500">
                {searchQuery || filterStatus !== "all" 
                  ? "Try adjusting your search or filter criteria."
                  : "No messages available for this brand yet."
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
