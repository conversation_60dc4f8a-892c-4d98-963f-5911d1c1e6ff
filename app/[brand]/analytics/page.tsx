"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BrandDropdown, type Brand } from "@/components/brand-dropdown"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from 'recharts'
import { getBrandBySlug, brandMappings } from "@/utils/brand-utils"
import {
  RefreshCw,
  Download,
  TrendingUp,
  Clock,
  MessageSquare,
  Users,
  Brain,
  Target,
  BarChart3,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Activity,
  <PERSON>u,
  <PERSON><PERSON><PERSON><PERSON>
} from "lucide-react"
import Link from "next/link"

// Sample data for charts
const responseTimeData = [
  { name: '<PERSON>', value: 1.2 },
  { name: 'Tu<PERSON>', value: 1.1 },
  { name: 'Wed', value: 1.3 },
  { name: 'Thu', value: 0.9 },
  { name: 'Fri', value: 1.4 },
  { name: 'Sat', value: 1.5 },
  { name: 'Sun', value: 1.3 }
]

const sentimentData = [
  { name: 'Positive', value: 65.4, color: '#10B981' },
  { name: 'Negative', value: 19.1, color: '#EF4444' },
  { name: 'Neutral', value: 15.5, color: '#F59E0B' }
]

const initialBrands: Brand[] = brandMappings.map(brand => ({
  name: brand.name,
  active: false,
  messageCount: Math.floor(Math.random() * 20) + 1,
  analyticsCount: null,
  expanded: false
}))

export default function BrandAnalyticsPage() {
  const params = useParams()
  const brandSlug = params.brand as string
  const brandInfo = getBrandBySlug(brandSlug)
  
  const [selectedPeriod, setSelectedPeriod] = useState("Last 7 days")
  const [activeTab, setActiveTab] = useState("overview")
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [brandStates, setBrandStates] = useState<Brand[]>(
    initialBrands.map(brand => ({
      ...brand,
      active: brand.name === brandInfo?.name
    }))
  )

  const handleBrandUpdate = (updatedBrands: Brand[]) => {
    setBrandStates(updatedBrands)
  }

  const handleAnalyticsClick = (brandName: string) => {
    const targetBrand = brandMappings.find(b => b.name === brandName)
    if (targetBrand) {
      window.location.href = `/${targetBrand.slug}/analytics`
    }
  }

  const handleMessagesClick = (brandName: string) => {
    const targetBrand = brandMappings.find(b => b.name === brandName)
    if (targetBrand) {
      window.location.href = `/${targetBrand.slug}/messages`
    }
  }

  if (!brandInfo) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Brand Not Found</h1>
          <p className="text-gray-600 mb-4">The brand "{brandSlug}" could not be found.</p>
          <Link href="/" className="text-blue-600 hover:text-blue-800">
            Return to Home
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="hidden lg:flex w-64 bg-white border-r border-gray-200 flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
              AC
            </div>
            <span className="font-medium text-gray-900">AC Social Command</span>
          </div>
        </div>

        {/* Brands List */}
        <BrandDropdown
          brands={brandStates}
          onBrandUpdate={handleBrandUpdate}
          onAnalyticsClick={handleAnalyticsClick}
          onMessagesClick={handleMessagesClick}
        />

        {/* Navigation */}
        <div className="p-4 border-t border-gray-200">
          <div className="space-y-1">
            <div className="flex items-center space-x-2 p-2 bg-blue-50 text-blue-700 rounded-lg">
              <BarChart3 className="w-4 h-4" />
              <span className="text-sm font-medium">Analytics</span>
            </div>
            <Link 
              href={`/${brandSlug}/messages`}
              className="flex items-center space-x-2 p-2 text-gray-600 hover:bg-gray-50 rounded-lg cursor-pointer"
            >
              <MessageSquare className="w-4 h-4" />
              <span className="text-sm">Messages</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 lg:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="flex items-center space-x-3">
              <Link href="/" className="lg:hidden">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="w-4 h-4" />
                </Button>
              </Link>
              <Button
                variant="outline"
                size="sm"
                className="lg:hidden"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <Menu className="w-4 h-4" />
              </Button>
              <div>
                <h1 className="text-xl lg:text-2xl font-bold text-gray-900">{brandInfo.fullName} - Analytics</h1>
                <p className="text-gray-600 mt-1 text-sm lg:text-base">AI performance metrics and brand response insights</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Learning Active
              </Badge>
              <Button variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4 lg:p-6">
          {/* Brand Analytics Header */}
          <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-6 gap-4">
            <h2 className="text-lg lg:text-xl font-semibold text-gray-900">Brand Analytics</h2>
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-full sm:w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Last 7 days">Last 7 days</SelectItem>
                  <SelectItem value="Last 30 days">Last 30 days</SelectItem>
                  <SelectItem value="Last 90 days">Last 90 days</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* AI Accuracy */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">AI Accuracy</CardTitle>
                <Brain className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">94.2%</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  <span>+2.1%</span>
                  <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 text-xs">
                    vs last period
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Response Time */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Response Time</CardTitle>
                <Clock className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.3s</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  <span>-12.4%</span>
                  <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 text-xs">
                    vs last period
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Messages Processed */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Messages Processed</CardTitle>
                <MessageSquare className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,847</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  <span>+15.2%</span>
                  <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 text-xs">
                    vs last period
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Engagement Rate */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Engagement Rate</CardTitle>
                <Users className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87.5%</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  <span>+3.2%</span>
                  <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 text-xs">
                    vs last period
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Learning Progress */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Learning Progress</CardTitle>
                <Activity className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8.7/10</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  <span>+0.3</span>
                  <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 text-xs">
                    vs last period
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Accuracy Score */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Accuracy Score</CardTitle>
                <Target className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">127.3h</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  <span>+22.5%</span>
                  <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 text-xs">
                    vs last period
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Navigation Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
              <TabsTrigger value="overview" className="text-xs lg:text-sm">Overview</TabsTrigger>
              <TabsTrigger value="performance" className="text-xs lg:text-sm">Performance</TabsTrigger>
              <TabsTrigger value="sentiment" className="text-xs lg:text-sm">Sentiment</TabsTrigger>
              <TabsTrigger value="platforms" className="text-xs lg:text-sm">Platforms</TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Response Time Trend */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">Response Time Trend</CardTitle>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    Avg 2.3s
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={responseTimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="value" 
                      stroke="#EF4444" 
                      strokeWidth={2}
                      dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Sentiment Distribution */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">Sentiment Distribution</CardTitle>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    65% Positive
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={sentimentData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {sentimentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="flex justify-center space-x-6 mt-4">
                  {sentimentData.map((item, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: item.color }}
                      ></div>
                      <span className="text-sm text-gray-600">
                        {item.name} {item.value}%
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
