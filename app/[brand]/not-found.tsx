import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Home } from "lucide-react"

export default function BrandNotFound() {
  return (
    <div className="flex h-screen items-center justify-center bg-gray-50">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-2xl font-bold text-red-600">404</span>
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Brand Not Found</h1>
        <p className="text-gray-600 mb-6">
          The brand you're looking for doesn't exist or may have been moved.
        </p>
        
        <div className="space-y-3">
          <Link href="/">
            <Button className="w-full">
              <Home className="w-4 h-4 mr-2" />
              Return to Home
            </Button>
          </Link>
          
          <Link href="/">
            <Button variant="outline" className="w-full">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </Link>
        </div>
        
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Available Brands</h3>
          <p className="text-xs text-blue-700">
            NYX, Belif, La Roche-Posay, Maybelline, Thayers, EyeBuyDirect, THINK, Blueair
          </p>
        </div>
      </div>
    </div>
  )
}
