"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { BrandDropdown, type Brand } from "@/components/brand-dropdown"
import {
  Bar<PERSON>hart3,
  MessageSquare,
  Building2,
  Bo<PERSON>
} from "lucide-react"

const initialBrands: Brand[] = [
  {
    name: "NYX Professional...",
    active: true,
    messageCount: 12,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "<PERSON><PERSON>",
    active: false,
    messageCount: 8,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "<PERSON>",
    active: false,
    messageCount: 15,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "Maybelline Ne...",
    active: false,
    messageCount: 3,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "<PERSON>hayers Natural...",
    active: false,
    messageCount: 7,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "EyeBuyDirect",
    active: false,
    messageCount: 5,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    active: false,
    messageCount: 2,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "<PERSON><PERSON>",
    active: false,
    messageCount: 9,
    analyticsCount: null,
    expanded: false
  },
  {
    name: "This is a Very Lo...",
    active: false,
    messageCount: 4,
    analyticsCount: null,
    expanded: false
  }
]

export default function Home() {
  const [brandStates, setBrandStates] = useState<Brand[]>(initialBrands)

  const handleBrandUpdate = (updatedBrands: Brand[]) => {
    setBrandStates(updatedBrands)
  }



  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="hidden lg:flex w-64 bg-white border-r border-gray-200 flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
              AC
            </div>
            <span className="font-medium text-gray-900">AC Social Command</span>
          </div>
        </div>

        {/* Brands List */}
        <BrandDropdown
          brands={brandStates}
          onBrandUpdate={handleBrandUpdate}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Main Dashboard Content */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-4xl w-full">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">AC Social Command</h1>
              <p className="text-lg text-gray-600 mb-2">
                Streamline social media responses across beauty and lifestyle brands with
              </p>
              <p className="text-lg text-gray-600 mb-4">AI-powered assistance</p>
              <p className="text-sm text-gray-500">Powered by Artisan Council</p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Beauty Brands */}
              <Card className="text-center">
                <CardHeader className="pb-2">
                  <div className="flex justify-center mb-2">
                    <Building2 className="w-8 h-8 text-gray-400" />
                  </div>
                  <CardTitle className="text-sm font-medium text-gray-600">Beauty Brands</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900 mb-1">8</div>
                  <p className="text-xs text-gray-500">Active Accounts</p>
                </CardContent>
              </Card>

              {/* Pending Responses - Display only, no functionality */}
              <Card className="text-center">
                <CardHeader className="pb-2">
                  <div className="flex justify-center mb-2">
                    <MessageSquare className="w-8 h-8 text-gray-400" />
                  </div>
                  <CardTitle className="text-sm font-medium text-gray-600">Pending Responses</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900 mb-1">23</div>
                  <p className="text-xs text-gray-500">Awaiting Review</p>
                </CardContent>
              </Card>

              {/* AC AI Assistant */}
              <Card className="text-center">
                <CardHeader className="pb-2">
                  <div className="flex justify-center mb-2">
                    <Bot className="w-8 h-8 text-green-500" />
                  </div>
                  <CardTitle className="text-sm font-medium text-gray-600">AC AI Assistant</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="mb-1">
                    <Badge variant="secondary" className="bg-green-100 text-green-800 text-sm px-3 py-1">
                      Ready
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500">Ready to Assist</p>
                </CardContent>
              </Card>
            </div>

            {/* Getting Started */}
            <div className="text-center mt-12">
              <div className="bg-blue-50 rounded-lg p-6 max-w-md mx-auto">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Get Started</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Select a brand from the sidebar to view analytics and manage messages
                </p>
                <div className="flex items-center justify-center text-blue-600">
                  <BarChart3 className="w-4 h-4 mr-1" />
                  <span className="text-sm">Choose a brand to begin</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}