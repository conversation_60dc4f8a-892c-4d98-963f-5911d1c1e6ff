@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles and CSS variables */
:root {
  --theme-hue: 330; /* Pink */

  /* Primary colors */
  --primary-color: hsl(var(--theme-hue), 81%, 60%);
  --primary-dark: hsl(var(--theme-hue), 81%, 40%);
  --primary-light: hsl(var(--theme-hue), 70%, 80%);
  --primary-lighter: hsl(var(--theme-hue), 80%, 90%);
  --primary-lightest: hsl(var(--theme-hue), 100%, 97%);

  /* Text colors */
  --text-color: #333;
  --text-light: #666;
  --text-lighter: #999;

  /* Background colors */
  --background-color: #fff;
  --background-gradient: linear-gradient(to bottom, var(--primary-lightest), var(--background-color));

  /* UI colors */
  --border-color: var(--primary-light);
  --muted-color: #f3f4f6;
  --muted-hover: #e5e7eb;
  --success-color: #059669;
  --success-light: #d1fae5;
  --danger-color: #ef4444;
  --danger-light: #fee2e2;

  /* Dimensions */
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Glassmorphism */
  --glass-background: rgba(255, 255, 255, 0.8);
  --glass-blur: blur(10px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #121212;
    --background-gradient: linear-gradient(to bottom, hsl(var(--theme-hue), 30%, 10%), var(--background-color));
    --text-color: #f3f4f6;
    --text-light: #d1d5db;
    --text-lighter: #9ca3af;
    --glass-background: rgba(18, 18, 18, 0.8);
    --glass-border: 1px solid rgba(255, 255, 255, 0.1);
    --muted-color: #27272a;
    --muted-hover: #3f3f46;
    --primary-lightest: hsl(var(--theme-hue), 30%, 15%);
  }
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

body {
  color: var(--text-color);
  background-color: var(--background-color);
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-lighter);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}

/* Improved focus states */
:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(219, 39, 119, 0.3);
  transition: box-shadow 0.2s ease;
}

/* Analytics Dashboard Specific Styles */
.analytics-metric-card {
  transition: all 0.2s ease;
}

.analytics-metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.analytics-sidebar-brand {
  transition: all 0.15s ease;
}

.analytics-sidebar-brand:hover {
  background-color: #f9fafb;
}

.analytics-sidebar-brand.active {
  background-color: #eff6ff;
  color: #1d4ed8;
  border: 1px solid #dbeafe;
}

.analytics-chart-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.analytics-metric-trend {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.analytics-metric-trend.positive {
  color: #059669;
}

.analytics-metric-trend.negative {
  color: #dc2626;
}

/* Recharts customizations */
.recharts-tooltip-wrapper {
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.recharts-default-tooltip {
  border-radius: 8px !important;
  border: none !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

